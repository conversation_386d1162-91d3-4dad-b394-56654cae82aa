# Jorvik Website

A modern, responsive landing page for Jorvik - a collection of utilities for creating and managing ETL pipelines with PySpark.

## 🚀 Overview

This website showcases the Jorvik library, providing comprehensive information about its PySpark utilities, installation instructions, code examples, and community resources. Built with Next.js 15, TypeScript, and Tailwind CSS for a modern, performant experience.

## 🛠 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Animations**: Framer Motion
- **Package Manager**: npm

## 📋 Prerequisites

- Node.js 18+
- npm or yarn package manager

## 🚀 Installation & Setup

### 1. Clone the repository
```bash
git clone <repository-url>
cd jorvik-website
```

### 2. Install dependencies
```bash
npm install
```

### 3. Run the development server
```bash
npm run dev
```

### 4. Open your browser
Navigate to [http://localhost:3000](http://localhost:3000) to view the website.

## 📜 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🏗 Project Structure

```
src/
├── app/
│   ├── layout.tsx          # Root layout with metadata
│   ├── page.tsx            # Homepage
│   └── globals.css         # Global styles
├── components/
│   ├── Header.tsx          # Navigation header
│   ├── Hero.tsx            # Hero section
│   ├── Features.tsx        # Features showcase
│   ├── CodeExamples.tsx    # Interactive code examples
│   ├── Installation.tsx    # Installation guide
│   ├── QuickStart.tsx      # Quick start tutorial
│   ├── UseCases.tsx        # Use cases and scenarios
│   ├── Performance.tsx     # Performance metrics
│   ├── Comparison.tsx      # Framework comparison
│   ├── Team.tsx            # About the project
│   ├── Community.tsx       # Community resources
│   └── Footer.tsx          # Site footer
```

## 🎨 Design Features

- **Responsive Design**: Mobile-first approach with breakpoints for all devices
- **Modern UI**: Clean, technical aesthetic with data visualization elements
- **Interactive Elements**: Copy-to-clipboard code examples, hover animations
- **Smooth Animations**: Framer Motion for page transitions and micro-interactions
- **Accessibility**: Semantic HTML and proper ARIA labels
- **Performance**: Optimized images and code splitting

## 📝 Content Sections

The website includes the following sections:

1. **Hero** - Main headline and value proposition
2. **Features** - Key capabilities and utilities
3. **Code Examples** - Interactive code demonstrations
4. **Installation** - Setup instructions and requirements
5. **Quick Start** - Step-by-step getting started guide
6. **Use Cases** - Common scenarios and applications
7. **Performance** - PySpark integration benefits
8. **Comparison** - How Jorvik compares to alternatives
9. **About** - Project information and statistics
10. **Community** - GitHub links and contribution info
11. **Footer** - Additional links and resources

## 🤖 AI Development History

This website was created using Augment Code AI assistant. Below are the key prompts used during development:

### Initial Creation
1. **"Consult the README and create a website with this specification"**
   - Created the initial Next.js project structure
   - Built all components based on original README requirements
   - Implemented modern design with Tailwind CSS and animations

### Content Accuracy Update
2. **"I like the way the website looks, but the content is of course not correct. Now I have cloned the jorvik library onto this computer. You can look at the files here: @/Users/<USER>/code/jorvik. Adjust all the content of the website to reflect the actual library."**
   - Analyzed the actual Jorvik codebase
   - Updated all content to accurately reflect the library's current state
   - Changed from "data engineering framework" to "PySpark ETL utilities"
   - Updated code examples to show actual `add_column` function
   - Revised installation instructions to match development setup
   - Updated use cases to be realistic for a utility library
   - Modified team section to reflect early-stage project status

### Documentation
3. **"stop the server"**
   - Stopped the development server

4. **"Rewrite the README from scratch. Include the commands needed to install and run the website locally. Also create a section that lists all the prompts used to create the website using augment code."**
   - Complete README rewrite with installation instructions
   - Added AI development history section
   - Included project structure and technical details

## 🔗 Related Links

- [Jorvik Library Repository](https://github.com/jorvik-io/jorvik)
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS](https://tailwindcss.com)
- [Framer Motion](https://www.framer.com/motion/)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request