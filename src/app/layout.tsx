import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Jorvik - PySpark ETL Utilities',
  description: 'A collection of utilities for creating and managing ETL pipelines with PySpark. Simplify your data processing workflows.',
  keywords: 'pyspark, ETL, data pipelines, python, utilities, spark, data engineering',
  authors: [{ name: 'Jorvik Team' }],
  openGraph: {
    title: 'Jorvik - PySpark ETL Utilities',
    description: 'A collection of utilities for creating and managing ETL pipelines with PySpark. Simplify your data processing workflows.',
    url: 'https://jorvik.io',
    siteName: 'Jorvik',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Jorvik - PySpark ETL Utilities',
    description: 'A collection of utilities for creating and managing ETL pipelines with PySpark. Simplify your data processing workflows.',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.className} antialiased`}>
        {children}
      </body>
    </html>
  )
}
