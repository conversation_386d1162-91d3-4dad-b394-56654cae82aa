import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Jorvik Consulting - Expert PySpark & Data Engineering Services',
  description: 'Transform your data pipelines with the creators of Jorvik. Expert consulting, custom development, and training for PySpark and data engineering projects.',
  keywords: 'jorvik consulting, pyspark consulting, data engineering services, ETL consulting, spark consulting, data pipeline consulting',
  authors: [{ name: 'Jorvik Consulting Team' }],
  openGraph: {
    title: 'Jorvik Consulting - Expert PySpark & Data Engineering Services',
    description: 'Transform your data pipelines with the creators of Jorvik. Expert consulting, custom development, and training for PySpark and data engineering projects.',
    url: 'https://jorvik.io',
    siteName: 'Jorvik Consulting',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Jorvik Consulting - Expert PySpark & Data Engineering Services',
    description: 'Transform your data pipelines with the creators of Jorvik. Expert consulting, custom development, and training for PySpark and data engineering projects.',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.className} antialiased`}>
        {children}
      </body>
    </html>
  )
}
