import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Jorvik - Open Source Data Engineering Framework',
  description: 'A powerful, simple, and scalable Python framework for building data pipelines, ETL processes, and data engineering workflows.',
  keywords: 'data engineering, python, ETL, data pipelines, open source, framework',
  authors: [{ name: 'Jorvik Team' }],
  openGraph: {
    title: 'Jorvik - Open Source Data Engineering Framework',
    description: 'A powerful, simple, and scalable Python framework for building data pipelines, ETL processes, and data engineering workflows.',
    url: 'https://jorvik.io',
    siteName: 'Jorvik',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Jorvik - Open Source Data Engineering Framework',
    description: 'A powerful, simple, and scalable Python framework for building data pipelines, ETL processes, and data engineering workflows.',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.className} antialiased`}>
        {children}
      </body>
    </html>
  )
}
