'use client'

import { useState } from 'react'
import { CheckCir<PERSON>, Co<PERSON>, Check, Terminal, Play, ArrowRight } from 'lucide-react'
import { motion } from 'framer-motion'

export default function QuickStart() {
  const [copiedStep, setCopiedStep] = useState<number | null>(null)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])

  const steps = [
    {
      title: 'Setup Java & Clone Repository',
      description: 'Install Java and clone the Jorvik repository',
      command: `# Install Java (macOS)
brew install openjdk@17

# Clone repository
git clone https://github.com/jorvik-io/jorvik.git
cd jorvik`,
      time: '2 minutes'
    },
    {
      title: 'Install Jorvik',
      description: 'Install in development mode with testing dependencies',
      command: `# Install with test dependencies
pip install -e '.[tests]'

# Verify installation
pytest test/`,
      time: '1 minute'
    },
    {
      title: 'Create Your First ETL Script',
      description: 'Use Jorvik utilities in a simple PySpark ETL pipeline',
      command: `# my_etl.py
from pyspark.sql import SparkSession, functions as F
from jorvik.dummy import add_column

# Initialize Spark
spark = SparkSession.builder.appName("MyETL").getOrCreate()

# Create sample data
df = spark.createDataFrame([
    {"id": 1, "name": "Alice", "salary": 50000},
    {"id": 2, "name": "Bob", "salary": 60000}
])

# Add columns using Jorvik utilities
df_with_bonus = add_column(df, "bonus", F.col("salary") * 0.1)
df_final = add_column(df_with_bonus, "processed_at", F.current_timestamp())

df_final.show()`,
      time: '3 minutes'
    },
    {
      title: 'Run Your ETL Pipeline',
      description: 'Execute your pipeline and see the results',
      command: `# Run your ETL script
python my_etl.py

# Expected output:
# +---+-----+------+----------+-------------------+
# | id| name|salary|     bonus|       processed_at|
# +---+-----+------+----------+-------------------+
# |  1|Alice| 50000|    5000.0|2024-01-15 10:30:00|
# |  2|  Bob| 60000|    6000.0|2024-01-15 10:30:00|
# +---+-----+------+----------+-------------------+`,
      time: '30 seconds'
    }
  ]

  const copyToClipboard = async (text: string, stepIndex: number) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedStep(stepIndex)
      setTimeout(() => setCopiedStep(null), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  const toggleStepCompletion = (stepIndex: number) => {
    if (completedSteps.includes(stepIndex)) {
      setCompletedSteps(completedSteps.filter(i => i !== stepIndex))
    } else {
      setCompletedSteps([...completedSteps, stepIndex])
    }
  }

  return (
    <section id="quickstart" className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-primary-50 to-accent-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
              Quick Start Guide
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Get your first data pipeline running in under 5 minutes. Follow these simple steps 
              to experience the power of Jorvik.
            </p>
          </motion.div>
        </div>

        <div className="max-w-4xl mx-auto">
          {steps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="mb-8"
            >
              <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-secondary-200">
                {/* Step Header */}
                <div className="p-6 border-b border-secondary-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <button
                        onClick={() => toggleStepCompletion(index)}
                        className={`mr-4 p-2 rounded-full transition-colors ${
                          completedSteps.includes(index)
                            ? 'bg-green-100 text-green-600'
                            : 'bg-secondary-100 text-secondary-400 hover:bg-secondary-200'
                        }`}
                      >
                        <CheckCircle className="w-6 h-6" />
                      </button>
                      <div>
                        <h3 className="text-xl font-semibold text-secondary-900">
                          Step {index + 1}: {step.title}
                        </h3>
                        <p className="text-secondary-600 mt-1">{step.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center text-sm text-secondary-500">
                      <Terminal className="w-4 h-4 mr-1" />
                      {step.time}
                    </div>
                  </div>
                </div>

                {/* Step Content */}
                <div className="relative">
                  <div className="absolute top-4 right-4 z-10">
                    <button
                      onClick={() => copyToClipboard(step.command, index)}
                      className="bg-secondary-800 hover:bg-secondary-700 text-white p-2 rounded-md transition-colors flex items-center"
                    >
                      {copiedStep === index ? (
                        <Check className="w-4 h-4" />
                      ) : (
                        <Copy className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                  
                  <pre className="bg-secondary-900 text-secondary-100 p-6 overflow-x-auto">
                    <code className="text-sm font-mono leading-relaxed">
                      {step.command}
                    </code>
                  </pre>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Next Steps */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-white rounded-xl p-8 shadow-lg border border-secondary-200">
            <h3 className="text-2xl font-bold text-secondary-900 mb-4">
              🎉 Congratulations! You've Built Your First Pipeline
            </h3>
            <p className="text-secondary-600 mb-8 max-w-2xl mx-auto">
              You now have a working data pipeline! Ready to explore more advanced features 
              like real-time streaming, complex transformations, and production deployment?
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="p-4 bg-primary-50 rounded-lg border border-primary-200">
                <h4 className="font-semibold text-primary-900 mb-2">📚 Learn More</h4>
                <p className="text-primary-700 text-sm mb-3">
                  Dive deeper with our comprehensive documentation and tutorials
                </p>
                <a
                  href="https://docs.jorvik.io"
                  className="text-primary-600 hover:text-primary-700 font-medium text-sm"
                >
                  View Documentation →
                </a>
              </div>
              
              <div className="p-4 bg-accent-50 rounded-lg border border-accent-200">
                <h4 className="font-semibold text-accent-900 mb-2">💬 Get Help</h4>
                <p className="text-accent-700 text-sm mb-3">
                  Join our community for support, tips, and best practices
                </p>
                <a
                  href="#community"
                  className="text-accent-600 hover:text-accent-700 font-medium text-sm"
                >
                  Join Community →
                </a>
              </div>
              
              <div className="p-4 bg-secondary-50 rounded-lg border border-secondary-200">
                <h4 className="font-semibold text-secondary-900 mb-2">🚀 Deploy</h4>
                <p className="text-secondary-700 text-sm mb-3">
                  Learn how to deploy your pipelines to production environments
                </p>
                <a
                  href="https://docs.jorvik.io/deployment"
                  className="text-secondary-600 hover:text-secondary-700 font-medium text-sm"
                >
                  Deployment Guide →
                </a>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#usecases"
                className="bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors inline-flex items-center justify-center"
              >
                <Play className="mr-2 w-5 h-5" />
                Explore Use Cases
              </a>
              <a
                href="https://github.com/jorvik-io/jorvik-examples"
                className="border-2 border-primary-600 text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors inline-flex items-center justify-center"
              >
                More Examples
                <ArrowRight className="ml-2 w-5 h-5" />
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
