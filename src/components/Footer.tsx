'use client'

import { 
  Github, 
  Twitter, 
  Linkedin, 
  MessageCircle, 
  BookOpen, 
  Mail,
  MapPin,
  ExternalLink
} from 'lucide-react'

export default function Footer() {
  const footerLinks = {
    services: [
      { name: 'Implementation', href: '#services' },
      { name: 'Custom Development', href: '#services' },
      { name: 'Performance Optimization', href: '#services' },
      { name: 'Training & Workshops', href: '#services' },
      { name: 'Ongoing Support', href: '#services' }
    ],
    process: [
      { name: 'Discovery & Assessment', href: '#process' },
      { name: 'Solution Design', href: '#process' },
      { name: 'Implementation', href: '#process' },
      { name: 'Knowledge Transfer', href: '#process' },
      { name: 'Ongoing Support', href: '#process' }
    ],
    resources: [
      { name: 'Jorvik Library', href: 'https://github.com/jorvik-io/jorvik' },
      { name: 'Case Studies', href: '/case-studies' },
      { name: 'Blog', href: '/blog' },
      { name: 'Whitepapers', href: '/resources' },
      { name: 'Webinars', href: '/webinars' }
    ],
    company: [
      { name: 'About Us', href: '#team' },
      { name: 'Our Team', href: '#team' },
      { name: 'Careers', href: '/careers' },
      { name: 'Contact', href: '#contact' },
      { name: 'Partners', href: '/partners' }
    ],
    legal: [
      { name: 'Privacy Policy', href: '/privacy' },
      { name: 'Terms of Service', href: '/terms' },
      { name: 'Data Security', href: '/security' },
      { name: 'Open Source License', href: 'https://github.com/jorvik-io/jorvik/blob/main/LICENSE' }
    ]
  }

  const socialLinks = [
    { name: 'GitHub', icon: Github, href: 'https://github.com/jorvik-io/jorvik' },
    { name: 'Discord', icon: MessageCircle, href: 'https://discord.gg/jorvik' },
    { name: 'Twitter', icon: Twitter, href: 'https://twitter.com/jorvik_io' },
    { name: 'LinkedIn', icon: Linkedin, href: 'https://linkedin.com/company/jorvik-io' }
  ]

  return (
    <footer className="bg-secondary-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-12">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center mb-4">
              <h3 className="text-2xl font-bold gradient-text">Jorvik</h3>
            </div>
            <p className="text-secondary-300 mb-6 leading-relaxed">
              Expert consulting services for PySpark and data engineering projects.
              Transform your data challenges into competitive advantages with the creators of Jorvik.
            </p>
            
            {/* Social Links */}
            <div className="flex space-x-4">
              {socialLinks.map((social) => {
                const Icon = social.icon
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    className="bg-secondary-800 p-2 rounded-lg hover:bg-secondary-700 transition-colors"
                    aria-label={social.name}
                  >
                    <Icon className="w-5 h-5" />
                  </a>
                )
              })}
            </div>
          </div>

          {/* Services Links */}
          <div>
            <h4 className="font-semibold text-white mb-4">Services</h4>
            <ul className="space-y-3">
              {footerLinks.services.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-secondary-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Process Links */}
          <div>
            <h4 className="font-semibold text-white mb-4">Process</h4>
            <ul className="space-y-3">
              {footerLinks.process.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-secondary-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources Links */}
          <div>
            <h4 className="font-semibold text-white mb-4">Resources</h4>
            <ul className="space-y-3">
              {footerLinks.resources.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-secondary-300 hover:text-white transition-colors text-sm flex items-center"
                  >
                    {link.name}
                    {link.href.startsWith('http') && (
                      <ExternalLink className="w-3 h-3 ml-1" />
                    )}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h4 className="font-semibold text-white mb-4">Company</h4>
            <ul className="space-y-3">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-secondary-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="border-t border-secondary-800 pt-8 mb-8">
          <div className="max-w-md">
            <h4 className="font-semibold text-white mb-2">Stay Connected</h4>
            <p className="text-secondary-300 text-sm mb-4">
              Get insights, case studies, and updates on data engineering best practices.
            </p>
            <div className="flex">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-2 bg-secondary-800 border border-secondary-700 rounded-l-lg text-white placeholder-secondary-400 focus:outline-none focus:border-primary-500"
              />
              <button className="bg-primary-600 hover:bg-primary-700 px-6 py-2 rounded-r-lg transition-colors">
                <Mail className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-secondary-800 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6 mb-4 md:mb-0">
              <p className="text-secondary-400 text-sm">
                © 2024 Jorvik Consulting. All rights reserved.
              </p>
              <div className="flex items-center text-secondary-400 text-sm">
                <MapPin className="w-4 h-4 mr-1" />
                San Francisco, CA
              </div>
            </div>
            
            <div className="flex flex-wrap justify-center md:justify-end space-x-6">
              {footerLinks.legal.map((link) => (
                <a
                  key={link.name}
                  href={link.href}
                  className="text-secondary-400 hover:text-white transition-colors text-sm flex items-center"
                >
                  {link.name}
                  {link.href.startsWith('http') && (
                    <ExternalLink className="w-3 h-3 ml-1" />
                  )}
                </a>
              ))}
            </div>
          </div>
        </div>

        {/* Open Source Badge */}
        <div className="mt-8 text-center">
          <div className="inline-flex items-center px-4 py-2 bg-secondary-800 rounded-full">
            <Github className="w-4 h-4 mr-2" />
            <span className="text-sm text-secondary-300">
              Proudly Open Source
            </span>
            <span className="mx-2 text-secondary-600">•</span>
            <a
              href="https://github.com/jorvik-io/jorvik"
              className="text-primary-400 hover:text-primary-300 text-sm transition-colors"
            >
              Star us on GitHub
            </a>
          </div>
        </div>
      </div>
    </footer>
  )
}
