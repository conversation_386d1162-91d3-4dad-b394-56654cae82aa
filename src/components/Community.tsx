'use client'

import { 
  MessageCircle, 
  Github, 
  BookOpen, 
  Users, 
  Calendar,
  ExternalLink,
  Star,
  GitFork,
  MessageSquare,
  Zap
} from 'lucide-react'
import { motion } from 'framer-motion'

export default function Community() {
  const communityChannels = [
    {
      name: 'Discord',
      icon: MessageCircle,
      description: 'Join our active Discord community for real-time discussions, help, and networking',
      members: '5,000+',
      link: 'https://discord.gg/jorvik',
      color: 'primary',
      activity: 'Very Active'
    },
    {
      name: 'GitHub',
      icon: Github,
      description: 'Contribute to the project, report issues, and collaborate with the core team',
      members: '2,500+',
      link: 'https://github.com/jorvik-io/jorvik',
      color: 'secondary',
      activity: 'Daily Updates'
    },
    {
      name: 'Forum',
      icon: MessageSquare,
      description: 'In-depth discussions, tutorials, and community-driven Q&A',
      members: '3,200+',
      link: 'https://forum.jorvik.io',
      color: 'accent',
      activity: 'Active'
    },
    {
      name: 'Documentation',
      icon: BookOpen,
      description: 'Comprehensive guides, API references, and community-contributed tutorials',
      members: 'Open to All',
      link: 'https://docs.jorvik.io',
      color: 'primary',
      activity: 'Updated Daily'
    }
  ]

  const stats = [
    { label: 'GitHub Stars', value: '12.5K', icon: Star },
    { label: 'Contributors', value: '500+', icon: Users },
    { label: 'Forks', value: '2.1K', icon: GitFork },
    { label: 'Discord Members', value: '5K+', icon: MessageCircle }
  ]

  const events = [
    {
      title: 'Monthly Community Call',
      date: 'First Thursday of every month',
      time: '10:00 AM PST',
      description: 'Join our monthly community call to discuss roadmap, new features, and community contributions',
      type: 'Recurring'
    },
    {
      title: 'Jorvik Conference 2024',
      date: 'September 15-16, 2024',
      time: 'San Francisco, CA',
      description: 'Our annual conference featuring talks from industry experts, workshops, and networking',
      type: 'Conference'
    },
    {
      title: 'Contributor Workshop',
      date: 'Every Saturday',
      time: '2:00 PM PST',
      description: 'Weekly workshop for new contributors to learn about the codebase and contribution process',
      type: 'Workshop'
    }
  ]

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'primary':
        return {
          bg: 'bg-primary-100',
          text: 'text-primary-600',
          border: 'border-primary-200',
          hover: 'hover:bg-primary-50'
        }
      case 'accent':
        return {
          bg: 'bg-accent-100',
          text: 'text-accent-600',
          border: 'border-accent-200',
          hover: 'hover:bg-accent-50'
        }
      case 'secondary':
        return {
          bg: 'bg-secondary-100',
          text: 'text-secondary-600',
          border: 'border-secondary-200',
          hover: 'hover:bg-secondary-50'
        }
      default:
        return {
          bg: 'bg-primary-100',
          text: 'text-primary-600',
          border: 'border-primary-200',
          hover: 'hover:bg-primary-50'
        }
    }
  }

  return (
    <section id="community" className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-primary-50 to-accent-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
              Join Our
              <span className="gradient-text"> Community</span>
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Connect with thousands of data engineers, get help, share knowledge, and contribute 
              to the future of data engineering. Everyone is welcome!
            </p>
          </motion.div>
        </div>

        {/* Community Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center p-6 bg-white rounded-xl shadow-lg border border-secondary-200"
              >
                <div className="bg-primary-100 p-3 rounded-full w-fit mx-auto mb-4">
                  <Icon className="w-6 h-6 text-primary-600" />
                </div>
                <div className="text-3xl font-bold text-secondary-900 mb-1">
                  {stat.value}
                </div>
                <div className="text-secondary-600 text-sm">
                  {stat.label}
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Community Channels */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {communityChannels.map((channel, index) => {
            const colors = getColorClasses(channel.color)
            const Icon = channel.icon
            
            return (
              <motion.div
                key={channel.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`bg-white rounded-xl p-6 shadow-lg border ${colors.border} ${colors.hover} transition-colors group`}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <div className={`${colors.bg} p-3 rounded-lg mr-4 group-hover:scale-110 transition-transform`}>
                      <Icon className={`w-6 h-6 ${colors.text}`} />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-secondary-900">
                        {channel.name}
                      </h3>
                      <div className="flex items-center text-sm text-secondary-500">
                        <Users className="w-4 h-4 mr-1" />
                        {channel.members}
                        <span className="mx-2">•</span>
                        <Zap className="w-4 h-4 mr-1" />
                        {channel.activity}
                      </div>
                    </div>
                  </div>
                  <ExternalLink className="w-5 h-5 text-secondary-400 group-hover:text-secondary-600 transition-colors" />
                </div>
                
                <p className="text-secondary-600 mb-4 leading-relaxed">
                  {channel.description}
                </p>
                
                <a
                  href={channel.link}
                  className={`inline-flex items-center ${colors.text} hover:opacity-70 transition-opacity font-medium`}
                >
                  Join {channel.name}
                  <ExternalLink className="ml-2 w-4 h-4" />
                </a>
              </motion.div>
            )
          })}
        </div>

        {/* Events */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-secondary-900 mb-8 text-center">
            Upcoming Events
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {events.map((event, index) => (
              <div
                key={event.title}
                className="bg-white rounded-xl p-6 shadow-lg border border-secondary-200 hover:shadow-xl transition-shadow"
              >
                <div className="flex items-center justify-between mb-4">
                  <span className="px-3 py-1 bg-primary-100 text-primary-600 rounded-full text-sm font-medium">
                    {event.type}
                  </span>
                  <Calendar className="w-5 h-5 text-secondary-400" />
                </div>
                
                <h4 className="text-lg font-bold text-secondary-900 mb-2">
                  {event.title}
                </h4>
                
                <div className="text-sm text-secondary-600 mb-3">
                  <div className="flex items-center mb-1">
                    <Calendar className="w-4 h-4 mr-2" />
                    {event.date}
                  </div>
                  <div className="flex items-center">
                    <span className="w-4 h-4 mr-2 flex items-center justify-center">🕐</span>
                    {event.time}
                  </div>
                </div>
                
                <p className="text-secondary-600 text-sm leading-relaxed">
                  {event.description}
                </p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Contributing CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-secondary-900 to-primary-900 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Ready to Contribute?</h3>
            <p className="text-secondary-100 mb-6 max-w-2xl mx-auto">
              Whether you're fixing bugs, adding features, improving documentation, or helping 
              other community members, every contribution makes Jorvik better for everyone.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://github.com/jorvik-io/jorvik/blob/main/CONTRIBUTING.md"
                className="bg-white text-secondary-900 px-8 py-3 rounded-lg font-semibold hover:bg-secondary-50 transition-colors inline-flex items-center justify-center"
              >
                <Github className="mr-2 w-5 h-5" />
                Start Contributing
              </a>
              <a
                href="https://discord.gg/jorvik"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors inline-flex items-center justify-center"
              >
                <MessageCircle className="mr-2 w-5 h-5" />
                Join Discord
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
