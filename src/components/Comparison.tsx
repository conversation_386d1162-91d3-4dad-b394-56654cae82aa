'use client'

import { Check, X, Star, ArrowRight } from 'lucide-react'
import { motion } from 'framer-motion'

export default function Comparison() {
  const frameworks = [
    {
      name: '<PERSON><PERSON><PERSON>',
      logo: '🔧',
      description: 'PySpark utility library for ETL operations',
      isRecommended: true,
      features: {
        'Easy Setup': true,
        'Python Native': true,
        'PySpark Integration': true,
        'Type Safety': true,
        'Testing Support': true,
        'Utility Functions': true,
        'ETL Focused': true,
        'Lightweight': true,
        'Open Source': true,
        'Development Tools': true,
        'DataFrame Operations': true,
        'Active Development': true
      },
      pricing: 'Free',
      setupTime: '2 minutes',
      learningCurve: 'Easy'
    },
    {
      name: 'Raw PySpark',
      logo: '⚡',
      description: 'Direct PySpark DataFrame operations',
      isRecommended: false,
      features: {
        'Easy Setup': false,
        'Python Native': true,
        'PySpark Integration': true,
        'Type Safety': false,
        'Testing Support': false,
        'Utility Functions': false,
        'ETL Focused': false,
        'Lightweight': true,
        'Open Source': true,
        'Development Tools': false,
        'DataFrame Operations': true,
        'Active Development': true
      },
      pricing: 'Free',
      setupTime: '10 minutes',
      learningCurve: 'Moderate'
    },
    {
      name: 'Custom Utils',
      logo: '🛠️',
      description: 'Writing your own utility functions',
      isRecommended: false,
      features: {
        'Easy Setup': false,
        'Python Native': true,
        'PySpark Integration': true,
        'Type Safety': false,
        'Testing Support': false,
        'Utility Functions': false,
        'ETL Focused': false,
        'Lightweight': true,
        'Open Source': false,
        'Development Tools': false,
        'DataFrame Operations': true,
        'Active Development': false
      },
      pricing: 'Free',
      setupTime: '60+ minutes',
      learningCurve: 'Hard'
    },
    {
      name: 'Spark Libraries',
      logo: '📚',
      description: 'Third-party Spark utility libraries',
      isRecommended: false,
      features: {
        'Easy Setup': true,
        'Python Native': true,
        'PySpark Integration': true,
        'Type Safety': false,
        'Testing Support': false,
        'Utility Functions': true,
        'ETL Focused': false,
        'Lightweight': false,
        'Open Source': true,
        'Development Tools': false,
        'DataFrame Operations': true,
        'Active Development': true
      },
      pricing: 'Free',
      setupTime: '15 minutes',
      learningCurve: 'Moderate'
    }
  ]

  const featureKeys = Object.keys(frameworks[0].features)

  return (
    <section id="comparison" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
              How Jorvik Fits In
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Jorvik is a utility library that complements existing PySpark workflows.
              Here's how it compares to other approaches for PySpark development.
            </p>
          </motion.div>
        </div>

        {/* Comparison Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="bg-white rounded-xl shadow-lg overflow-hidden border border-secondary-200"
        >
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-secondary-50">
                  <th className="px-6 py-4 text-left text-sm font-semibold text-secondary-900 w-1/5">
                    Feature
                  </th>
                  {frameworks.map((framework) => (
                    <th key={framework.name} className="px-6 py-4 text-center relative">
                      {framework.isRecommended && (
                        <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                          <div className="bg-primary-600 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center">
                            <Star className="w-3 h-3 mr-1" />
                            Recommended
                          </div>
                        </div>
                      )}
                      <div className="pt-4">
                        <div className="text-2xl mb-2">{framework.logo}</div>
                        <div className="font-semibold text-secondary-900">{framework.name}</div>
                        <div className="text-xs text-secondary-600 mt-1">
                          {framework.description}
                        </div>
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-secondary-200">
                {/* Key Metrics */}
                <tr className="bg-secondary-25">
                  <td className="px-6 py-4 font-medium text-secondary-900">Setup Time</td>
                  {frameworks.map((framework) => (
                    <td key={framework.name} className="px-6 py-4 text-center text-secondary-700">
                      {framework.setupTime}
                    </td>
                  ))}
                </tr>
                <tr className="bg-secondary-25">
                  <td className="px-6 py-4 font-medium text-secondary-900">Learning Curve</td>
                  {frameworks.map((framework) => (
                    <td key={framework.name} className="px-6 py-4 text-center text-secondary-700">
                      {framework.learningCurve}
                    </td>
                  ))}
                </tr>
                <tr className="bg-secondary-25">
                  <td className="px-6 py-4 font-medium text-secondary-900">Pricing</td>
                  {frameworks.map((framework) => (
                    <td key={framework.name} className="px-6 py-4 text-center text-secondary-700">
                      {framework.pricing}
                    </td>
                  ))}
                </tr>

                {/* Features */}
                {featureKeys.map((feature) => (
                  <tr key={feature} className="hover:bg-secondary-50">
                    <td className="px-6 py-4 font-medium text-secondary-900">{feature}</td>
                    {frameworks.map((framework) => (
                      <td key={framework.name} className="px-6 py-4 text-center">
                        {framework.features[feature] ? (
                          <Check className="w-5 h-5 text-green-600 mx-auto" />
                        ) : (
                          <X className="w-5 h-5 text-red-400 mx-auto" />
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </motion.div>

        {/* Why Choose Jorvik */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="mt-16 grid grid-cols-1 lg:grid-cols-2 gap-8"
        >
          <div className="bg-gradient-to-br from-primary-50 to-accent-50 rounded-xl p-8 border border-primary-200">
            <h3 className="text-2xl font-bold text-secondary-900 mb-6">
              Why Choose Jorvik?
            </h3>
            <ul className="space-y-4">
              {[
                {
                  title: 'Developer Experience First',
                  description: 'Intuitive APIs, excellent documentation, and minimal setup time'
                },
                {
                  title: 'Performance Optimized',
                  description: 'Built from the ground up for speed and efficiency'
                },
                {
                  title: 'Modern Architecture',
                  description: 'Cloud-native, containerized, and designed for modern infrastructure'
                },
                {
                  title: 'Comprehensive Solution',
                  description: 'Everything you need in one framework - no need to stitch together multiple tools'
                }
              ].map((item, index) => (
                <li key={index} className="flex items-start">
                  <div className="bg-primary-100 p-2 rounded-lg mr-4 mt-1">
                    <Check className="w-4 h-4 text-primary-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-secondary-900 mb-1">{item.title}</h4>
                    <p className="text-secondary-600 text-sm">{item.description}</p>
                  </div>
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-white rounded-xl p-8 shadow-lg border border-secondary-200">
            <h3 className="text-2xl font-bold text-secondary-900 mb-6">
              Migration Made Easy
            </h3>
            <p className="text-secondary-600 mb-6">
              Already using another framework? We provide migration tools and guides to help you 
              transition smoothly to Jorvik without disrupting your existing workflows.
            </p>
            
            <div className="space-y-4">
              <div className="flex items-center p-3 bg-secondary-50 rounded-lg">
                <span className="text-2xl mr-3">🌪️</span>
                <div>
                  <div className="font-medium text-secondary-900">From Airflow</div>
                  <div className="text-sm text-secondary-600">Automated DAG conversion</div>
                </div>
              </div>
              <div className="flex items-center p-3 bg-secondary-50 rounded-lg">
                <span className="text-2xl mr-3">⚡</span>
                <div>
                  <div className="font-medium text-secondary-900">From Spark</div>
                  <div className="text-sm text-secondary-600">Job migration toolkit</div>
                </div>
              </div>
              <div className="flex items-center p-3 bg-secondary-50 rounded-lg">
                <span className="text-2xl mr-3">🔧</span>
                <div>
                  <div className="font-medium text-secondary-900">From dbt</div>
                  <div className="text-sm text-secondary-600">Model transformation guide</div>
                </div>
              </div>
            </div>

            <div className="mt-6">
              <a
                href="https://docs.jorvik.io/migration"
                className="text-primary-600 hover:text-primary-700 font-medium flex items-center"
              >
                View Migration Guides
                <ArrowRight className="ml-2 w-4 h-4" />
              </a>
            </div>
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-secondary-900 to-primary-900 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Ready to Make the Switch?</h3>
            <p className="text-secondary-100 mb-6 max-w-2xl mx-auto">
              Join the growing community of data engineers who have chosen Jorvik for their 
              data infrastructure. Experience the difference today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#quickstart"
                className="bg-white text-secondary-900 px-8 py-3 rounded-lg font-semibold hover:bg-secondary-50 transition-colors inline-flex items-center justify-center"
              >
                Get Started Now
                <ArrowRight className="ml-2 w-5 h-5" />
              </a>
              <a
                href="https://docs.jorvik.io/comparison"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors inline-flex items-center justify-center"
              >
                Detailed Comparison
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
