'use client'

import { useState } from 'react'
import { Copy, Check, Terminal, Package, Download } from 'lucide-react'
import { motion } from 'framer-motion'

export default function Installation() {
  const [copiedCommand, setCopiedCommand] = useState<string | null>(null)

  const installMethods = [
    {
      title: 'Development Install',
      icon: Package,
      command: 'git clone https://github.com/jorvik-io/jorvik.git\ncd jorvik\npip install -e \'.[tests]\'',
      description: 'Install in editable mode with testing dependencies'
    },
    {
      title: 'Basic Install',
      icon: Package,
      command: 'git clone https://github.com/jorvik-io/jorvik.git\ncd jorvik\npip install -e .',
      description: 'Install in editable mode for basic usage'
    },
    {
      title: 'Java Setup (macOS)',
      icon: Download,
      command: 'brew install openjdk@17\nexport JAVA_HOME=/opt/homebrew/opt/openjdk@17',
      description: 'Install Java 17 (required for PySpark)'
    },
    {
      title: 'Run Tests',
      icon: Terminal,
      command: 'pytest test/',
      description: 'Run the test suite to verify installation'
    }
  ]

  const requirements = [
    { name: 'Python', version: '3.7+', status: 'required' },
    { name: 'Java', version: '11 or 17', status: 'required' },
    { name: 'PySpark', version: 'Latest', status: 'required' },
    { name: 'pandas', version: 'Latest', status: 'required' },
    { name: 'pyarrow', version: 'Latest', status: 'required' },
    { name: 'pytest', version: 'Latest', status: 'optional' }
  ]

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedCommand(text)
      setTimeout(() => setCopiedCommand(null), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  return (
    <section id="installation" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
              Get Started in
              <span className="gradient-text"> 30 Seconds</span>
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Installing Jorvik is simple. Choose your preferred method and start building 
              data pipelines immediately.
            </p>
          </motion.div>
        </div>

        {/* Installation Methods */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {installMethods.map((method, index) => {
            const Icon = method.icon
            return (
              <motion.div
                key={method.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-secondary-50 rounded-xl p-6 border border-secondary-200 hover:shadow-lg transition-shadow"
              >
                <div className="flex items-center mb-4">
                  <div className="bg-primary-100 p-2 rounded-lg mr-3">
                    <Icon className="w-5 h-5 text-primary-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-secondary-900">
                    {method.title}
                  </h3>
                </div>
                
                <p className="text-secondary-600 mb-4">
                  {method.description}
                </p>
                
                <div className="relative bg-secondary-900 rounded-lg p-4">
                  <button
                    onClick={() => copyToClipboard(method.command)}
                    className="absolute top-2 right-2 text-secondary-400 hover:text-white transition-colors"
                  >
                    {copiedCommand === method.command ? (
                      <Check className="w-4 h-4" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </button>
                  <pre className="text-secondary-100 font-mono text-sm overflow-x-auto">
                    <code>{method.command}</code>
                  </pre>
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* System Requirements */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-primary-50 to-accent-50 rounded-xl p-8"
        >
          <h3 className="text-2xl font-bold text-secondary-900 mb-6 text-center">
            System Requirements
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {requirements.map((req, index) => (
              <div
                key={req.name}
                className="bg-white rounded-lg p-4 border border-secondary-200"
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-secondary-900">{req.name}</h4>
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${
                      req.status === 'required'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-blue-100 text-blue-800'
                    }`}
                  >
                    {req.status}
                  </span>
                </div>
                <p className="text-secondary-600 text-sm">{req.version}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Verification */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-white rounded-xl p-8 shadow-lg border border-secondary-200">
            <div className="flex items-center justify-center mb-4">
              <div className="bg-green-100 p-3 rounded-full">
                <Terminal className="w-8 h-8 text-green-600" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-secondary-900 mb-4">
              Verify Your Installation
            </h3>
            <p className="text-secondary-600 mb-6">
              Run this command to verify Jorvik is installed correctly:
            </p>
            
            <div className="relative bg-secondary-900 rounded-lg p-4 max-w-md mx-auto">
              <button
                onClick={() => copyToClipboard('python -c "from jorvik.dummy import add_column; print(\'Jorvik installed successfully!\')"')}
                className="absolute top-2 right-2 text-secondary-400 hover:text-white transition-colors"
              >
                {copiedCommand === 'python -c "from jorvik.dummy import add_column; print(\'Jorvik installed successfully!\')"' ? (
                  <Check className="w-4 h-4" />
                ) : (
                  <Copy className="w-4 h-4" />
                )}
              </button>
              <pre className="text-secondary-100 font-mono text-sm">
                <code>python -c "from jorvik.dummy import add_column; print('Jorvik installed successfully!')"</code>
              </pre>
            </div>
            
            <div className="mt-6">
              <a
                href="#quickstart"
                className="bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors inline-flex items-center"
              >
                Next: Quick Start Guide
                <Terminal className="ml-2 w-5 h-5" />
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
