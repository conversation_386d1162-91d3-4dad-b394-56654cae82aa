'use client'

import { Github, Linkedin, Twitter, MapPin, Users, Heart } from 'lucide-react'
import { motion } from 'framer-motion'

export default function Team() {
  const teamMembers = [
    {
      name: '<PERSON><PERSON>',
      role: 'Senior Software Engineer & Co-founder',
      bio: 'Former Senior Software Engineer at Maersk and Microsoft.',
      image: '👨🏻‍💻',
      location: 'Copenhagen, Denmark',
      expertise: ['PySpark Architecture', 'Distributed Systems', 'Performance Optimization'],
      social: {
        github: 'GiorgosPa',
        linkedin: 'georgios-papoutsakis'
      }
    },
    {
      name: '<PERSON>',
      role: 'Senior Data Engineer & Co-founder',
      bio: 'Former Data Engineer at Maersk.',
      image: '👩‍💻',
      location: 'Malmö, Sweden',
      expertise: ['ETL Design', 'Data Engineering', 'Data Versioning', 'Performance Tuning'],
      social: {
        github: 'kleverman',
        linkedin: 'marcus-kleverman-0396bb38'
      }
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      role: 'Senior Data Engineer & Co-founder',
      bio: 'Former Senior Data Engineer at Maersk',
      image: '👨‍🔬',
      location: 'Ho Chi Minh City, Vietnam',
      expertise: ['ETL Design', 'Real-time Processing', 'Performance Tuning'],
      social: {
        github: 'vunguyenq',
        linkedin: 'vunguyenq89'
      }
    },
    {
      name: 'Pimin <PERSON>falou<PERSON>',
      role: 'Senior Data Scientist & Co-founder',
      bio: 'Former Senior Data Scientist at Maersk.',
      image: '👩‍🎨',
      location: 'Copenhagen, Denmark',
      expertise: ['Enterprise Solutions', 'Team Training', 'Data Strategy'],
      social: {
        github: 'skipperkongen',
        linkedin: 'kostaskefaloukos'
      }
    }
  ]

  const stats = [
    { label: 'Team Members', value: '4', icon: Users },
    { label: 'Years Experience', value: '40+', icon: MapPin },
    { label: 'Projects Delivered', value: '100+', icon: Heart },
    { label: 'Client Satisfaction', value: '98%', icon: Github }
  ]

  return (
    <section id="team" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
              Meet Our Expert Team
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Our team of four seasoned data engineers and software architects brings decades of combined
              experience in building scalable data solutions. As the creators of Jorvik, we understand
              the challenges you face and have the expertise to solve them.
            </p>
          </motion.div>
        </div>

        {/* Team Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center p-6 bg-gradient-to-br from-primary-50 to-accent-50 rounded-xl border border-primary-200"
              >
                <div className="bg-primary-100 p-3 rounded-full w-fit mx-auto mb-4">
                  <Icon className="w-6 h-6 text-primary-600" />
                </div>
                <div className="text-3xl font-bold text-secondary-900 mb-1">
                  {stat.value}
                </div>
                <div className="text-secondary-600 text-sm">
                  {stat.label}
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Team Members */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {teamMembers.map((member, index) => (
            <motion.div
              key={member.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-xl p-6 shadow-lg border border-secondary-200 hover:shadow-xl transition-shadow group"
            >
              <div className="text-center mb-4">
                <div className="text-6xl mb-4 group-hover:scale-110 transition-transform">
                  {member.image}
                </div>
                <h3 className="text-xl font-bold text-secondary-900 mb-1">
                  {member.name}
                </h3>
                <p className="text-primary-600 font-medium mb-2">
                  {member.role}
                </p>
                <div className="flex items-center justify-center text-secondary-500 text-sm mb-4">
                  <MapPin className="w-4 h-4 mr-1" />
                  {member.location}
                </div>
              </div>
              
              <p className="text-secondary-600 text-sm leading-relaxed mb-4 text-center">
                {member.bio}
              </p>

              <div className="mb-6">
                <h5 className="font-semibold text-secondary-900 text-sm mb-2 text-center">Expertise</h5>
                <div className="flex flex-wrap justify-center gap-2">
                  {member.expertise.map((skill, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-primary-100 text-primary-700 rounded-full text-xs"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
              
              <div className="flex justify-center space-x-4">
                <a
                  href={`https://github.com/${member.social.github}`}
                  className="text-secondary-400 hover:text-secondary-600 transition-colors"
                >
                  <Github className="w-5 h-5" />
                </a>
                <a
                  href={`https://linkedin.com/in/${member.social.linkedin}`}
                  className="text-secondary-400 hover:text-secondary-600 transition-colors"
                >
                  <Linkedin className="w-5 h-5" />
                </a>
                <a
                  href={`https://twitter.com/${member.social.twitter}`}
                  className="text-secondary-400 hover:text-secondary-600 transition-colors"
                >
                  <Twitter className="w-5 h-5" />
                </a>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Company Values */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-secondary-900 to-primary-900 rounded-2xl p-8 text-white text-center"
        >
          <h3 className="text-2xl font-bold mb-6">Our Values</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="text-4xl mb-4">🚀</div>
              <h4 className="font-semibold mb-2">Innovation First</h4>
              <p className="text-secondary-100 text-sm">
                We push the boundaries of what's possible in data engineering
              </p>
            </div>
            <div>
              <div className="text-4xl mb-4">🤝</div>
              <h4 className="font-semibold mb-2">Community Driven</h4>
              <p className="text-secondary-100 text-sm">
                Our users and community are at the heart of everything we build
              </p>
            </div>
            <div>
              <div className="text-4xl mb-4">🌍</div>
              <h4 className="font-semibold mb-2">Open & Inclusive</h4>
              <p className="text-secondary-100 text-sm">
                We believe in open source and building an inclusive tech community
              </p>
            </div>
          </div>
        </motion.div>

        {/* Join Us CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-white rounded-xl p-8 shadow-lg border border-secondary-200">
            <h3 className="text-2xl font-bold text-secondary-900 mb-4">
              Want to Join Our Team?
            </h3>
            <p className="text-secondary-600 mb-6 max-w-2xl mx-auto">
              We're always looking for talented individuals who share our passion for data 
              engineering and open source. Check out our open positions or reach out to us.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://jobs.jorvik.io"
                className="bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors inline-flex items-center justify-center"
              >
                View Open Positions
                <Users className="ml-2 w-5 h-5" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="border-2 border-primary-600 text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors inline-flex items-center justify-center"
              >
                Get in Touch
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
