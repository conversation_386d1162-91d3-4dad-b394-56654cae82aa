'use client'

import { 
  Database, 
  GitBranch, 
  Zap, 
  Shield, 
  Monitor, 
  Layers,
  RefreshCw,
  BarChart3,
  Cloud,
  Lock
} from 'lucide-react'
import { motion } from 'framer-motion'

export default function Services() {
  const services = [
    {
      icon: Database,
      title: 'Implementation & Integration',
      description: 'Custom Jorvik implementations tailored to your specific data pipeline requirements and existing infrastructure.',
      color: 'primary'
    },
    {
      icon: GitBranch,
      title: 'Custom Development & Extensions',
      description: 'Build custom utilities and extensions on top of Jorvik to meet your unique business needs.',
      color: 'accent'
    },
    {
      icon: Zap,
      title: 'Performance Optimization',
      description: 'Optimize your existing PySpark pipelines for maximum performance and cost efficiency.',
      color: 'secondary'
    },
    {
      icon: Shield,
      title: 'Training & Workshops',
      description: 'Comprehensive training programs to upskill your team on Jorvik and PySpark best practices.',
      color: 'primary'
    },
    {
      icon: Monitor,
      title: 'Architectural Design',
      description: 'Design scalable, maintainable data architectures using Jorvik as the foundation.',
      color: 'accent'
    },
    {
      icon: Layers,
      title: 'Migration & Modernization',
      description: 'Migrate legacy ETL systems to modern Jorvik-based solutions with minimal disruption.',
      color: 'secondary'
    },
    {
      icon: RefreshCw,
      title: 'Ongoing Support',
      description: 'Continuous support and maintenance to ensure your Jorvik implementations stay optimized.',
      color: 'primary'
    },
    {
      icon: BarChart3,
      title: 'Data Strategy Consulting',
      description: 'Strategic guidance on data architecture, governance, and best practices for your organization.',
      color: 'accent'
    },
    {
      icon: Cloud,
      title: 'Cloud Migration',
      description: 'Seamlessly migrate your data pipelines to cloud platforms with Jorvik optimization.',
      color: 'secondary'
    }
  ]

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'primary':
        return {
          bg: 'bg-primary-100',
          text: 'text-primary-600',
          border: 'border-primary-200'
        }
      case 'accent':
        return {
          bg: 'bg-accent-100',
          text: 'text-accent-600',
          border: 'border-accent-200'
        }
      case 'secondary':
        return {
          bg: 'bg-secondary-100',
          text: 'text-secondary-600',
          border: 'border-secondary-200'
        }
      default:
        return {
          bg: 'bg-primary-100',
          text: 'text-primary-600',
          border: 'border-primary-200'
        }
    }
  }

  return (
    <section id="services" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
              Comprehensive Consulting
              <span className="block gradient-text">Services</span>
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              From initial assessment to full implementation, our team provides end-to-end
              consulting services to help you leverage Jorvik for your data engineering challenges.
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const colors = getColorClasses(service.color)
            const Icon = service.icon
            
            return (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`p-6 rounded-xl border ${colors.border} bg-gradient-to-br from-white to-${service.color}-50 hover:shadow-lg transition-shadow group`}
              >
                <div className={`${colors.bg} p-3 rounded-lg w-fit mb-4 group-hover:scale-110 transition-transform`}>
                  <Icon className={`w-6 h-6 ${colors.text}`} />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  {service.title}
                </h3>
                <p className="text-secondary-600 leading-relaxed">
                  {service.description}
                </p>
              </motion.div>
            )
          })}
        </div>

        {/* Call to action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-primary-600 to-accent-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Ready to Accelerate Your Data Projects?</h3>
            <p className="text-primary-100 mb-6 max-w-2xl mx-auto">
              Partner with the Jorvik experts to transform your data engineering challenges into competitive advantages.
            </p>
            <a
              href="#contact"
              className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors inline-flex items-center"
            >
              Schedule Consultation
              <Database className="ml-2 w-5 h-5" />
            </a>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
