'use client'

import { 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Clock,
  Send,
  CheckCircle,
  ArrowRight
} from 'lucide-react'
import { motion } from 'framer-motion'
import { useState } from 'react'

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    projectType: '',
    timeline: '',
    budget: '',
    message: ''
  })

  const contactInfo = [
    {
      icon: Mail,
      title: 'Email Us',
      value: '<EMAIL>',
      description: 'Get in touch for project inquiries'
    },
    {
      icon: Phone,
      title: 'Schedule a Call',
      value: '+****************',
      description: 'Direct line for urgent consultations'
    },
    {
      icon: MapPin,
      title: 'Our Locations',
      value: 'San Francisco, Seattle, NYC, Austin',
      description: 'Distributed team across major tech hubs'
    },
    {
      icon: Clock,
      title: 'Response Time',
      value: 'Within 24 hours',
      description: 'We respond to all inquiries promptly'
    }
  ]

  const projectTypes = [
    'New Implementation',
    'Migration & Modernization',
    'Performance Optimization',
    'Team Training',
    'Ongoing Support',
    'Custom Development',
    'Other'
  ]

  const timelines = [
    'ASAP (Rush)',
    '1-3 months',
    '3-6 months',
    '6-12 months',
    'Flexible'
  ]

  const budgetRanges = [
    'Under $25k',
    '$25k - $50k',
    '$50k - $100k',
    '$100k - $250k',
    '$250k+',
    'To be discussed'
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log('Form submitted:', formData)
    // You would typically send this to your backend
  }

  return (
    <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-primary-50 to-accent-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
              Let's Start Your
              <span className="gradient-text"> Project</span>
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Ready to transform your data engineering challenges into competitive advantages? 
              Get in touch with our team of Jorvik experts today.
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-bold text-secondary-900 mb-8">Get in Touch</h3>
            
            <div className="space-y-6 mb-8">
              {contactInfo.map((info, index) => {
                const Icon = info.icon
                return (
                  <div key={index} className="flex items-start">
                    <div className="bg-primary-100 p-3 rounded-lg mr-4">
                      <Icon className="w-5 h-5 text-primary-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-secondary-900 mb-1">{info.title}</h4>
                      <p className="text-primary-600 font-medium mb-1">{info.value}</p>
                      <p className="text-secondary-600 text-sm">{info.description}</p>
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Availability */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-secondary-200">
              <div className="flex items-center mb-4">
                <Calendar className="w-5 h-5 text-accent-600 mr-2" />
                <h4 className="font-semibold text-secondary-900">Current Availability</h4>
              </div>
              <p className="text-secondary-600 mb-4">
                We're currently accepting new projects with start dates in Q2 2024. 
                Rush projects may be accommodated based on scope and requirements.
              </p>
              <div className="flex items-center text-green-600">
                <CheckCircle className="w-4 h-4 mr-2" />
                <span className="text-sm font-medium">Accepting new clients</span>
              </div>
            </div>
          </motion.div>

          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="bg-white rounded-xl p-8 shadow-lg border border-secondary-200"
          >
            <h3 className="text-2xl font-bold text-secondary-900 mb-6">Project Inquiry Form</h3>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    required
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="Your full name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    name="email"
                    required
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Company
                </label>
                <input
                  type="text"
                  name="company"
                  value={formData.company}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Your company name"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Project Type
                  </label>
                  <select
                    name="projectType"
                    value={formData.projectType}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="">Select type</option>
                    {projectTypes.map((type) => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Timeline
                  </label>
                  <select
                    name="timeline"
                    value={formData.timeline}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="">Select timeline</option>
                    {timelines.map((timeline) => (
                      <option key={timeline} value={timeline}>{timeline}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Budget Range
                  </label>
                  <select
                    name="budget"
                    value={formData.budget}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="">Select budget</option>
                    {budgetRanges.map((budget) => (
                      <option key={budget} value={budget}>{budget}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Project Description *
                </label>
                <textarea
                  name="message"
                  required
                  rows={4}
                  value={formData.message}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Tell us about your project, current challenges, and what you're hoping to achieve..."
                />
              </div>

              <button
                type="submit"
                className="w-full bg-primary-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-primary-700 transition-colors flex items-center justify-center"
              >
                Send Project Inquiry
                <Send className="ml-2 w-5 h-5" />
              </button>
            </form>

            <div className="mt-6 p-4 bg-secondary-50 rounded-lg">
              <p className="text-secondary-600 text-sm">
                <strong>What happens next?</strong> We'll review your inquiry and respond within 24 hours 
                with next steps, including a proposed discovery call to discuss your project in detail.
              </p>
            </div>
          </motion.div>
        </div>

        {/* Alternative Contact Methods */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-white rounded-xl p-8 shadow-lg border border-secondary-200">
            <h3 className="text-xl font-bold text-secondary-900 mb-4">
              Prefer a Different Approach?
            </h3>
            <p className="text-secondary-600 mb-6">
              We're flexible in how we connect. Choose the method that works best for you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors inline-flex items-center justify-center"
              >
                <Mail className="mr-2 w-5 h-5" />
                Email Directly
              </a>
              <a
                href="tel:+15551234567"
                className="border-2 border-primary-600 text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors inline-flex items-center justify-center"
              >
                <Phone className="mr-2 w-5 h-5" />
                Call Us
              </a>
              <a
                href="https://calendly.com/jorvik-consulting"
                className="border-2 border-accent-600 text-accent-600 px-6 py-3 rounded-lg font-semibold hover:bg-accent-50 transition-colors inline-flex items-center justify-center"
              >
                <Calendar className="mr-2 w-5 h-5" />
                Book Meeting
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
