'use client'

import { useState } from 'react'
import { Co<PERSON>, Check, Play } from 'lucide-react'
import { motion } from 'framer-motion'

export default function CodeExamples() {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null)
  const [activeTab, setActiveTab] = useState(0)

  const examples = [
    {
      title: 'Adding Columns to DataFrames',
      description: 'Use Jorvik utilities to easily add new columns to your PySpark DataFrames',
      code: `from pyspark.sql import SparkSession, functions as F
from jorvik.dummy import add_column

# Initialize Spark session
spark = SparkSession.builder.appName("JorvikExample").getOrCreate()

# Create a sample DataFrame
df = spark.createDataFrame([
    {"id": 1, "name": "Alice", "age": 25},
    {"id": 2, "name": "<PERSON>", "age": 30},
    {"id": 3, "name": "<PERSON>", "age": 35}
])

# Add a new column using Jorvik utility
df_with_status = add_column(df, "status", F.lit("active"))

# Add another column with calculated values
df_final = add_column(df_with_status, "age_group",
                     F.when(F.col("age") < 30, "young")
                      .otherwise("mature"))

df_final.show()`
    },
    {
      title: 'Testing Your ETL Functions',
      description: 'Use the included test fixtures to test your PySpark ETL operations',
      code: `import pytest
from pyspark.sql import functions as F
from pyspark.testing import assertDataFrameEqual
from fixtures.spark import spark
from jorvik.dummy import add_column

def test_add_column_with_literal(spark):
    """Test adding a column with a literal value"""
    # Create test data
    df = spark.createDataFrame([
        {"id": 1, "name": "Alice"},
        {"id": 2, "name": "Bob"}
    ])

    # Add column using Jorvik utility
    result = add_column(df, "department", F.lit("Engineering"))

    # Expected result
    expected = spark.createDataFrame([
        {"id": 1, "name": "Alice", "department": "Engineering"},
        {"id": 2, "name": "Bob", "department": "Engineering"}
    ])

    # Assert DataFrames are equal
    assertDataFrameEqual(result, expected)

# Run with: pytest test/`
    },
    {
      title: 'Setting Up Development Environment',
      description: 'Get started with Jorvik development including Java setup and testing',
      code: `# Install Java (required for PySpark)
# On macOS:
brew install openjdk@17

# Set JAVA_HOME (add to your .env file)
export JAVA_HOME=/opt/homebrew/opt/openjdk@17

# Install Jorvik in development mode
pip install -e '.[tests]'

# Run the test suite
pytest test/

# Example ETL pipeline structure
from pyspark.sql import SparkSession
from jorvik.dummy import add_column

def create_etl_pipeline():
    spark = SparkSession.builder \\
        .appName("MyETLPipeline") \\
        .getOrCreate()

    # Extract: Read data from source
    df = spark.read.parquet("input/data.parquet")

    # Transform: Add processing timestamp
    df_transformed = add_column(df, "processed_at",
                               F.current_timestamp())

    # Load: Write to destination
    df_transformed.write.mode("overwrite") \\
        .parquet("output/processed_data.parquet")

    spark.stop()

if __name__ == "__main__":
    create_etl_pipeline()`
    }
  ]

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedIndex(index)
      setTimeout(() => setCopiedIndex(null), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  return (
    <section id="examples" className="py-20 px-4 sm:px-6 lg:px-8 bg-secondary-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
              See Jorvik in Action
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              From simple ETL jobs to complex real-time analytics, Jorvik makes data engineering 
              intuitive and powerful. Here are some examples to get you started.
            </p>
          </motion.div>
        </div>

        {/* Tab Navigation */}
        <div className="flex flex-wrap justify-center mb-8 bg-white rounded-lg p-2 shadow-sm">
          {examples.map((example, index) => (
            <button
              key={index}
              onClick={() => setActiveTab(index)}
              className={`px-6 py-3 rounded-md font-medium transition-colors ${
                activeTab === index
                  ? 'bg-primary-600 text-white'
                  : 'text-secondary-600 hover:text-primary-600 hover:bg-primary-50'
              }`}
            >
              {example.title}
            </button>
          ))}
        </div>

        {/* Code Example */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="bg-white rounded-xl shadow-lg overflow-hidden"
        >
          <div className="p-6 border-b border-secondary-200">
            <h3 className="text-2xl font-bold text-secondary-900 mb-2">
              {examples[activeTab].title}
            </h3>
            <p className="text-secondary-600">
              {examples[activeTab].description}
            </p>
          </div>
          
          <div className="relative">
            <div className="absolute top-4 right-4 z-10">
              <button
                onClick={() => copyToClipboard(examples[activeTab].code, activeTab)}
                className="bg-secondary-800 hover:bg-secondary-700 text-white p-2 rounded-md transition-colors flex items-center"
              >
                {copiedIndex === activeTab ? (
                  <Check className="w-4 h-4" />
                ) : (
                  <Copy className="w-4 h-4" />
                )}
              </button>
            </div>
            
            <pre className="bg-secondary-900 text-secondary-100 p-6 overflow-x-auto">
              <code className="text-sm font-mono leading-relaxed">
                {examples[activeTab].code}
              </code>
            </pre>
          </div>
        </motion.div>

        {/* Try it out CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <div className="bg-white rounded-xl p-8 shadow-lg">
            <h3 className="text-2xl font-bold text-secondary-900 mb-4">
              Ready to Try These Examples?
            </h3>
            <p className="text-secondary-600 mb-6 max-w-2xl mx-auto">
              Install Jorvik and run these examples in minutes. Our interactive documentation 
              includes step-by-step tutorials for each use case.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#installation"
                className="bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors inline-flex items-center justify-center"
              >
                <Play className="mr-2 w-5 h-5" />
                Install & Run
              </a>
              <a
                href="https://docs.jorvik.io/examples"
                className="border-2 border-primary-600 text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors inline-flex items-center justify-center"
              >
                View All Examples
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
