'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, Check, Play } from 'lucide-react'
import { motion } from 'framer-motion'

export default function CodeExamples() {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null)
  const [activeTab, setActiveTab] = useState(0)

  const examples = [
    {
      title: 'Simple ETL Pipeline',
      description: 'Extract data from a database, transform it, and load to another destination',
      code: `from jorvik import Pipeline, Source, Transform, Destination

# Define your pipeline
pipeline = Pipeline("user_analytics")

# Extract data from PostgreSQL
source = Source.postgres(
    connection="postgresql://user:pass@localhost/db",
    query="SELECT * FROM users WHERE created_at >= '2024-01-01'"
)

# Transform the data
@Transform
def clean_user_data(df):
    df['email'] = df['email'].str.lower()
    df['full_name'] = df['first_name'] + ' ' + df['last_name']
    return df[['id', 'email', 'full_name', 'created_at']]

# Load to data warehouse
destination = Destination.snowflake(
    connection="snowflake://account/db/schema",
    table="clean_users"
)

# Build and run the pipeline
pipeline.add_source(source)
pipeline.add_transform(clean_user_data)
pipeline.add_destination(destination)

pipeline.run()`
    },
    {
      title: 'Real-time Stream Processing',
      description: 'Process streaming data from Kafka with real-time transformations',
      code: `from jorvik import StreamPipeline, KafkaSource, Transform

# Create streaming pipeline
stream = StreamPipeline("real_time_analytics")

# Connect to Kafka stream
kafka_source = KafkaSource(
    bootstrap_servers="localhost:9092",
    topic="user_events",
    group_id="analytics_group"
)

@Transform.streaming
def process_events(event):
    # Real-time event processing
    event['processed_at'] = datetime.utcnow()
    event['user_segment'] = classify_user(event['user_id'])
    
    # Emit processed event
    return event

@Transform.window(window_size="5m", slide="1m")
def aggregate_metrics(events):
    # Windowed aggregations
    return {
        'window_start': events[0]['timestamp'],
        'total_events': len(events),
        'unique_users': len(set(e['user_id'] for e in events)),
        'avg_session_time': sum(e['session_time'] for e in events) / len(events)
    }

# Build streaming pipeline
stream.source(kafka_source)
stream.transform(process_events)
stream.transform(aggregate_metrics)
stream.sink_to_redis("analytics:metrics")

stream.start()`
    },
    {
      title: 'Complex Workflow Orchestration',
      description: 'Orchestrate multiple dependent tasks with error handling and retries',
      code: `from jorvik import Workflow, Task, Schedule
from jorvik.operators import PythonOperator, SQLOperator

# Define workflow
workflow = Workflow("daily_reporting")

# Task 1: Data validation
@Task(retries=3, retry_delay="5m")
def validate_data():
    # Custom validation logic
    quality_score = run_data_quality_checks()
    if quality_score < 0.95:
        raise ValueError("Data quality below threshold")
    return quality_score

# Task 2: Generate reports
generate_reports = SQLOperator(
    task_id="generate_reports",
    sql="""
    INSERT INTO daily_reports
    SELECT 
        date_trunc('day', created_at) as report_date,
        count(*) as total_users,
        sum(revenue) as total_revenue
    FROM transactions 
    WHERE created_at >= current_date - interval '1 day'
    GROUP BY 1
    """,
    depends_on=[validate_data]
)

# Task 3: Send notifications
@Task(depends_on=[generate_reports])
def send_notifications():
    report_data = get_latest_report()
    send_slack_message(f"Daily report ready: {report_data}")
    send_email_report(report_data)

# Schedule workflow
workflow.schedule(Schedule.daily(hour=6, minute=0))
workflow.run()`
    }
  ]

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedIndex(index)
      setTimeout(() => setCopiedIndex(null), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  return (
    <section id="examples" className="py-20 px-4 sm:px-6 lg:px-8 bg-secondary-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
              See Jorvik in Action
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              From simple ETL jobs to complex real-time analytics, Jorvik makes data engineering 
              intuitive and powerful. Here are some examples to get you started.
            </p>
          </motion.div>
        </div>

        {/* Tab Navigation */}
        <div className="flex flex-wrap justify-center mb-8 bg-white rounded-lg p-2 shadow-sm">
          {examples.map((example, index) => (
            <button
              key={index}
              onClick={() => setActiveTab(index)}
              className={`px-6 py-3 rounded-md font-medium transition-colors ${
                activeTab === index
                  ? 'bg-primary-600 text-white'
                  : 'text-secondary-600 hover:text-primary-600 hover:bg-primary-50'
              }`}
            >
              {example.title}
            </button>
          ))}
        </div>

        {/* Code Example */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="bg-white rounded-xl shadow-lg overflow-hidden"
        >
          <div className="p-6 border-b border-secondary-200">
            <h3 className="text-2xl font-bold text-secondary-900 mb-2">
              {examples[activeTab].title}
            </h3>
            <p className="text-secondary-600">
              {examples[activeTab].description}
            </p>
          </div>
          
          <div className="relative">
            <div className="absolute top-4 right-4 z-10">
              <button
                onClick={() => copyToClipboard(examples[activeTab].code, activeTab)}
                className="bg-secondary-800 hover:bg-secondary-700 text-white p-2 rounded-md transition-colors flex items-center"
              >
                {copiedIndex === activeTab ? (
                  <Check className="w-4 h-4" />
                ) : (
                  <Copy className="w-4 h-4" />
                )}
              </button>
            </div>
            
            <pre className="bg-secondary-900 text-secondary-100 p-6 overflow-x-auto">
              <code className="text-sm font-mono leading-relaxed">
                {examples[activeTab].code}
              </code>
            </pre>
          </div>
        </motion.div>

        {/* Try it out CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <div className="bg-white rounded-xl p-8 shadow-lg">
            <h3 className="text-2xl font-bold text-secondary-900 mb-4">
              Ready to Try These Examples?
            </h3>
            <p className="text-secondary-600 mb-6 max-w-2xl mx-auto">
              Install Jorvik and run these examples in minutes. Our interactive documentation 
              includes step-by-step tutorials for each use case.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#installation"
                className="bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors inline-flex items-center justify-center"
              >
                <Play className="mr-2 w-5 h-5" />
                Install & Run
              </a>
              <a
                href="https://docs.jorvik.io/examples"
                className="border-2 border-primary-600 text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors inline-flex items-center justify-center"
              >
                View All Examples
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
