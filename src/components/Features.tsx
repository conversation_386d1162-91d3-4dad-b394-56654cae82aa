'use client'

import { 
  Database, 
  GitBranch, 
  Zap, 
  Shield, 
  Monitor, 
  Layers,
  RefreshCw,
  BarChart3,
  Cloud,
  Lock
} from 'lucide-react'
import { motion } from 'framer-motion'

export default function Features() {
  const features = [
    {
      icon: Database,
      title: 'Unified Data Processing',
      description: 'Handle batch and streaming data with a single, consistent API. Connect to any data source or destination.',
      color: 'primary'
    },
    {
      icon: GitBranch,
      title: 'Pipeline Orchestration',
      description: 'Build complex workflows with dependency management, error handling, and automatic retries.',
      color: 'accent'
    },
    {
      icon: Zap,
      title: 'High Performance',
      description: 'Optimized for speed with parallel processing, smart caching, and efficient memory usage.',
      color: 'secondary'
    },
    {
      icon: Shield,
      title: 'Enterprise Ready',
      description: 'Built-in security, monitoring, and compliance features for production environments.',
      color: 'primary'
    },
    {
      icon: Monitor,
      title: 'Real-time Monitoring',
      description: 'Comprehensive observability with metrics, logs, and distributed tracing out of the box.',
      color: 'accent'
    },
    {
      icon: Layers,
      title: 'Modular Architecture',
      description: 'Extensible plugin system allows you to customize and extend functionality as needed.',
      color: 'secondary'
    },
    {
      icon: RefreshCw,
      title: 'Auto-scaling',
      description: 'Automatically scale your pipelines based on data volume and processing requirements.',
      color: 'primary'
    },
    {
      icon: BarChart3,
      title: 'Data Quality',
      description: 'Built-in data validation, profiling, and quality monitoring to ensure data integrity.',
      color: 'accent'
    },
    {
      icon: Cloud,
      title: 'Cloud Native',
      description: 'Deploy anywhere - on-premises, cloud, or hybrid environments with Kubernetes support.',
      color: 'secondary'
    }
  ]

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'primary':
        return {
          bg: 'bg-primary-100',
          text: 'text-primary-600',
          border: 'border-primary-200'
        }
      case 'accent':
        return {
          bg: 'bg-accent-100',
          text: 'text-accent-600',
          border: 'border-accent-200'
        }
      case 'secondary':
        return {
          bg: 'bg-secondary-100',
          text: 'text-secondary-600',
          border: 'border-secondary-200'
        }
      default:
        return {
          bg: 'bg-primary-100',
          text: 'text-primary-600',
          border: 'border-primary-200'
        }
    }
  }

  return (
    <section id="features" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
              Everything You Need for
              <span className="block gradient-text">Modern Data Engineering</span>
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Jorvik provides a comprehensive toolkit for building, deploying, and managing 
              data pipelines at scale. From simple ETL jobs to complex real-time analytics.
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const colors = getColorClasses(feature.color)
            const Icon = feature.icon
            
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`p-6 rounded-xl border ${colors.border} bg-gradient-to-br from-white to-${feature.color}-50 hover:shadow-lg transition-shadow group`}
              >
                <div className={`${colors.bg} p-3 rounded-lg w-fit mb-4 group-hover:scale-110 transition-transform`}>
                  <Icon className={`w-6 h-6 ${colors.text}`} />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-secondary-600 leading-relaxed">
                  {feature.description}
                </p>
              </motion.div>
            )
          })}
        </div>

        {/* Call to action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-primary-600 to-accent-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Ready to Transform Your Data Workflows?</h3>
            <p className="text-primary-100 mb-6 max-w-2xl mx-auto">
              Join thousands of data engineers who trust Jorvik to power their most critical data pipelines.
            </p>
            <a
              href="#quickstart"
              className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors inline-flex items-center"
            >
              Start Building Today
              <Database className="ml-2 w-5 h-5" />
            </a>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
