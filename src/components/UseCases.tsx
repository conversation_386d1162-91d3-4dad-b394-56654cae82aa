'use client'

import { 
  <PERSON><PERSON>dingUp, 
  ShoppingCart, 
  Shield, 
  Zap, 
  BarChart3, 
  Globe,
  ArrowRight,
  CheckCircle
} from 'lucide-react'
import { motion } from 'framer-motion'

export default function UseCases() {
  const useCases = [
    {
      icon: TrendingUp,
      title: 'Real-time Analytics',
      description: 'Process streaming data from multiple sources to generate real-time insights and dashboards.',
      features: [
        'Stream processing from Kafka, Kinesis',
        'Real-time aggregations and metrics',
        'Live dashboard updates',
        'Anomaly detection and alerting'
      ],
      companies: ['Netflix', 'Uber', 'Spotify'],
      color: 'primary'
    },
    {
      icon: ShoppingCart,
      title: 'E-commerce Data Pipeline',
      description: 'Unify customer data, inventory, and sales metrics across multiple channels and platforms.',
      features: [
        'Multi-channel data integration',
        'Customer 360-degree view',
        'Inventory optimization',
        'Personalization engines'
      ],
      companies: ['Amazon', 'Shopify', 'Zalando'],
      color: 'accent'
    },
    {
      icon: Shield,
      title: 'Fraud Detection',
      description: 'Build sophisticated fraud detection systems with real-time transaction monitoring.',
      features: [
        'Real-time transaction scoring',
        'ML model integration',
        'Risk assessment pipelines',
        'Compliance reporting'
      ],
      companies: ['PayPal', 'Stripe', 'Square'],
      color: 'secondary'
    },
    {
      icon: Zap,
      title: 'IoT Data Processing',
      description: 'Handle massive volumes of sensor data from IoT devices with edge computing support.',
      features: [
        'High-throughput data ingestion',
        'Edge processing capabilities',
        'Time-series data handling',
        'Predictive maintenance'
      ],
      companies: ['Tesla', 'GE', 'Siemens'],
      color: 'primary'
    },
    {
      icon: BarChart3,
      title: 'Business Intelligence',
      description: 'Create comprehensive BI pipelines that transform raw data into actionable business insights.',
      features: [
        'Data warehouse automation',
        'ETL/ELT processes',
        'Report generation',
        'KPI monitoring'
      ],
      companies: ['Salesforce', 'HubSpot', 'Tableau'],
      color: 'accent'
    },
    {
      icon: Globe,
      title: 'Multi-Cloud Data Migration',
      description: 'Seamlessly migrate and sync data across different cloud providers and on-premises systems.',
      features: [
        'Cross-cloud data sync',
        'Zero-downtime migration',
        'Data validation and integrity',
        'Cost optimization'
      ],
      companies: ['Microsoft', 'Google', 'AWS'],
      color: 'secondary'
    }
  ]

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'primary':
        return {
          bg: 'bg-primary-100',
          text: 'text-primary-600',
          border: 'border-primary-200',
          gradient: 'from-primary-50 to-primary-100'
        }
      case 'accent':
        return {
          bg: 'bg-accent-100',
          text: 'text-accent-600',
          border: 'border-accent-200',
          gradient: 'from-accent-50 to-accent-100'
        }
      case 'secondary':
        return {
          bg: 'bg-secondary-100',
          text: 'text-secondary-600',
          border: 'border-secondary-200',
          gradient: 'from-secondary-50 to-secondary-100'
        }
      default:
        return {
          bg: 'bg-primary-100',
          text: 'text-primary-600',
          border: 'border-primary-200',
          gradient: 'from-primary-50 to-primary-100'
        }
    }
  }

  return (
    <section id="usecases" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
              Proven Use Cases
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              From startups to Fortune 500 companies, Jorvik powers critical data infrastructure 
              across industries. Here are some popular use cases.
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {useCases.map((useCase, index) => {
            const colors = getColorClasses(useCase.color)
            const Icon = useCase.icon
            
            return (
              <motion.div
                key={useCase.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`p-8 rounded-xl border ${colors.border} bg-gradient-to-br ${colors.gradient} hover:shadow-lg transition-shadow group`}
              >
                <div className="flex items-start mb-6">
                  <div className={`${colors.bg} p-3 rounded-lg mr-4 group-hover:scale-110 transition-transform`}>
                    <Icon className={`w-8 h-8 ${colors.text}`} />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-secondary-900 mb-2">
                      {useCase.title}
                    </h3>
                    <p className="text-secondary-600 leading-relaxed">
                      {useCase.description}
                    </p>
                  </div>
                </div>

                <div className="mb-6">
                  <h4 className="font-semibold text-secondary-900 mb-3">Key Features:</h4>
                  <ul className="space-y-2">
                    {useCase.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-secondary-700">
                        <CheckCircle className={`w-4 h-4 mr-2 ${colors.text}`} />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-secondary-500 mb-1">Used by companies like:</p>
                    <div className="flex flex-wrap gap-2">
                      {useCase.companies.map((company, companyIndex) => (
                        <span
                          key={companyIndex}
                          className="px-2 py-1 bg-white rounded-full text-xs font-medium text-secondary-600 border border-secondary-200"
                        >
                          {company}
                        </span>
                      ))}
                    </div>
                  </div>
                  <button className={`${colors.text} hover:opacity-70 transition-opacity`}>
                    <ArrowRight className="w-5 h-5" />
                  </button>
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Success Stories CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-secondary-900 to-primary-900 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Ready to Build Your Use Case?</h3>
            <p className="text-secondary-100 mb-6 max-w-2xl mx-auto">
              Join thousands of data engineers who have successfully implemented these patterns 
              with Jorvik. Get started with our templates and best practices.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://docs.jorvik.io/use-cases"
                className="bg-white text-secondary-900 px-8 py-3 rounded-lg font-semibold hover:bg-secondary-50 transition-colors inline-flex items-center justify-center"
              >
                View Use Case Templates
                <ArrowRight className="ml-2 w-5 h-5" />
              </a>
              <a
                href="#performance"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors inline-flex items-center justify-center"
              >
                See Performance Metrics
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
