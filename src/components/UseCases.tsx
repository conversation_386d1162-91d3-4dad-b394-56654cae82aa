'use client'

import { 
  TrendingUp, 
  ShoppingCart, 
  Shield, 
  Zap, 
  BarChart3, 
  Globe,
  ArrowRight,
  CheckCircle
} from 'lucide-react'
import { motion } from 'framer-motion'

export default function UseCases() {
  const useCases = [
    {
      icon: TrendingUp,
      title: 'Data Enrichment Pipelines',
      description: 'Add calculated columns, timestamps, and derived fields to your datasets using Jorvik utilities.',
      features: [
        'Add processing timestamps',
        'Calculate derived metrics',
        'Enrich with lookup data',
        'Apply business rules'
      ],
      companies: ['Data Teams', 'Analytics Teams', 'ETL Developers'],
      color: 'primary'
    },
    {
      icon: ShoppingCart,
      title: 'Data Transformation Workflows',
      description: 'Standardize common DataFrame operations across your ETL pipelines with reusable utilities.',
      features: [
        'Consistent column operations',
        'Standardized transformations',
        'Reusable utility functions',
        'Type-safe operations'
      ],
      companies: ['Data Engineers', 'Backend Teams', 'Analytics'],
      color: 'accent'
    },
    {
      icon: Shield,
      title: 'Testing ETL Pipelines',
      description: 'Use Jorvik test fixtures to ensure your PySpark ETL operations work correctly.',
      features: [
        'Spark session fixtures',
        'DataFrame testing utilities',
        'Unit test support',
        'CI/CD integration'
      ],
      companies: ['QA Teams', 'DevOps', 'Data Engineers'],
      color: 'secondary'
    },
    {
      icon: Zap,
      title: 'Rapid Prototyping',
      description: 'Quickly prototype ETL operations with pre-built utility functions for common tasks.',
      features: [
        'Fast development cycles',
        'Common operation patterns',
        'Reduced boilerplate code',
        'Easy experimentation'
      ],
      companies: ['Data Scientists', 'Analysts', 'Developers'],
      color: 'primary'
    },
    {
      icon: BarChart3,
      title: 'Data Quality Checks',
      description: 'Implement consistent data quality patterns using standardized utility functions.',
      features: [
        'Column validation utilities',
        'Data type consistency',
        'Null value handling',
        'Quality metrics'
      ],
      companies: ['Data Governance', 'Quality Teams', 'Analysts'],
      color: 'accent'
    },
    {
      icon: Globe,
      title: 'Learning PySpark',
      description: 'Educational resource for learning PySpark best practices through well-documented utilities.',
      features: [
        'Clear code examples',
        'Best practice patterns',
        'Type annotations',
        'Comprehensive tests'
      ],
      companies: ['Students', 'New Developers', 'Training Teams'],
      color: 'secondary'
    }
  ]

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'primary':
        return {
          bg: 'bg-primary-100',
          text: 'text-primary-600',
          border: 'border-primary-200',
          gradient: 'from-primary-50 to-primary-100'
        }
      case 'accent':
        return {
          bg: 'bg-accent-100',
          text: 'text-accent-600',
          border: 'border-accent-200',
          gradient: 'from-accent-50 to-accent-100'
        }
      case 'secondary':
        return {
          bg: 'bg-secondary-100',
          text: 'text-secondary-600',
          border: 'border-secondary-200',
          gradient: 'from-secondary-50 to-secondary-100'
        }
      default:
        return {
          bg: 'bg-primary-100',
          text: 'text-primary-600',
          border: 'border-primary-200',
          gradient: 'from-primary-50 to-primary-100'
        }
    }
  }

  return (
    <section id="usecases" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
              Common Use Cases
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Jorvik utilities can be integrated into various PySpark ETL workflows.
              Here are some common scenarios where these utilities prove helpful.
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {useCases.map((useCase, index) => {
            const colors = getColorClasses(useCase.color)
            const Icon = useCase.icon
            
            return (
              <motion.div
                key={useCase.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`p-8 rounded-xl border ${colors.border} bg-gradient-to-br ${colors.gradient} hover:shadow-lg transition-shadow group`}
              >
                <div className="flex items-start mb-6">
                  <div className={`${colors.bg} p-3 rounded-lg mr-4 group-hover:scale-110 transition-transform`}>
                    <Icon className={`w-8 h-8 ${colors.text}`} />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-secondary-900 mb-2">
                      {useCase.title}
                    </h3>
                    <p className="text-secondary-600 leading-relaxed">
                      {useCase.description}
                    </p>
                  </div>
                </div>

                <div className="mb-6">
                  <h4 className="font-semibold text-secondary-900 mb-3">Key Features:</h4>
                  <ul className="space-y-2">
                    {useCase.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-secondary-700">
                        <CheckCircle className={`w-4 h-4 mr-2 ${colors.text}`} />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-secondary-500 mb-1">Used by companies like:</p>
                    <div className="flex flex-wrap gap-2">
                      {useCase.companies.map((company, companyIndex) => (
                        <span
                          key={companyIndex}
                          className="px-2 py-1 bg-white rounded-full text-xs font-medium text-secondary-600 border border-secondary-200"
                        >
                          {company}
                        </span>
                      ))}
                    </div>
                  </div>
                  <button className={`${colors.text} hover:opacity-70 transition-opacity`}>
                    <ArrowRight className="w-5 h-5" />
                  </button>
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Success Stories CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-secondary-900 to-primary-900 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Ready to Build Your Use Case?</h3>
            <p className="text-secondary-100 mb-6 max-w-2xl mx-auto">
              Join thousands of data engineers who have successfully implemented these patterns 
              with Jorvik. Get started with our templates and best practices.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://docs.jorvik.io/use-cases"
                className="bg-white text-secondary-900 px-8 py-3 rounded-lg font-semibold hover:bg-secondary-50 transition-colors inline-flex items-center justify-center"
              >
                View Use Case Templates
                <ArrowRight className="ml-2 w-5 h-5" />
              </a>
              <a
                href="#performance"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors inline-flex items-center justify-center"
              >
                See Performance Metrics
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
