'use client'

import { ArrowRight, Play, Database, Zap, Code } from 'lucide-react'
import { motion } from 'framer-motion'

export default function Hero() {
  return (
    <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold text-secondary-900 mb-6">
              Expert Jorvik
              <span className="block gradient-text">Consulting Services</span>
            </h1>
            <p className="text-xl sm:text-2xl text-secondary-600 mb-8 max-w-4xl mx-auto leading-relaxed">
              Transform your data pipelines with the creators of Jorvik. Our team of experts delivers
              custom PySpark solutions, training, and ongoing support to accelerate your data engineering projects.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
          >
            <a
              href="#contact"
              className="bg-primary-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-primary-700 transition-colors flex items-center group"
            >
              Schedule a Consultation
              <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </a>
            <a
              href="#services"
              className="border-2 border-primary-600 text-primary-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-primary-50 transition-colors flex items-center"
            >
              <Play className="mr-2 w-5 h-5" />
              Our Services
            </a>
          </motion.div>

          {/* Feature highlights */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
          >
            <div className="flex flex-col items-center p-6 bg-white/50 rounded-xl backdrop-blur-sm border border-white/20">
              <div className="bg-primary-100 p-3 rounded-full mb-4">
                <Database className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="text-lg font-semibold text-secondary-900 mb-2">Expert Implementation</h3>
              <p className="text-secondary-600 text-center">
                Custom Jorvik solutions designed and built by the creators
              </p>
            </div>

            <div className="flex flex-col items-center p-6 bg-white/50 rounded-xl backdrop-blur-sm border border-white/20">
              <div className="bg-accent-100 p-3 rounded-full mb-4">
                <Zap className="w-8 h-8 text-accent-600" />
              </div>
              <h3 className="text-lg font-semibold text-secondary-900 mb-2">Proven Results</h3>
              <p className="text-secondary-600 text-center">
                Accelerate your data engineering projects with battle-tested expertise
              </p>
            </div>

            <div className="flex flex-col items-center p-6 bg-white/50 rounded-xl backdrop-blur-sm border border-white/20">
              <div className="bg-secondary-100 p-3 rounded-full mb-4">
                <Code className="w-8 h-8 text-secondary-600" />
              </div>
              <h3 className="text-lg font-semibold text-secondary-900 mb-2">Full Support</h3>
              <p className="text-secondary-600 text-center">
                From initial consultation to ongoing maintenance and optimization
              </p>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Background decoration */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-40 -right-32 w-80 h-80 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-slow"></div>
        <div className="absolute -bottom-40 -left-32 w-80 h-80 bg-accent-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-slow"></div>
      </div>
    </section>
  )
}
