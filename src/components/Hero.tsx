'use client'

import { ArrowR<PERSON>, Play, Database, Zap, Code } from 'lucide-react'
import { motion } from 'framer-motion'

export default function Hero() {
  return (
    <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold text-secondary-900 mb-6">
              PySpark ETL Utilities
              <span className="block gradient-text">Made Simple</span>
            </h1>
            <p className="text-xl sm:text-2xl text-secondary-600 mb-8 max-w-4xl mx-auto leading-relaxed">
              Jorvik is a collection of utilities for creating and managing ETL pipelines with PySpark.
              Streamline your data processing workflows with helpful utility functions and best practices.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
          >
            <a
              href="#quickstart"
              className="bg-primary-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-primary-700 transition-colors flex items-center group"
            >
              Get Started
              <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </a>
            <a
              href="#examples"
              className="border-2 border-primary-600 text-primary-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-primary-50 transition-colors flex items-center"
            >
              <Play className="mr-2 w-5 h-5" />
              View Examples
            </a>
          </motion.div>

          {/* Feature highlights */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
          >
            <div className="flex flex-col items-center p-6 bg-white/50 rounded-xl backdrop-blur-sm border border-white/20">
              <div className="bg-primary-100 p-3 rounded-full mb-4">
                <Database className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="text-lg font-semibold text-secondary-900 mb-2">PySpark Utilities</h3>
              <p className="text-secondary-600 text-center">
                Helpful functions for common PySpark DataFrame operations
              </p>
            </div>

            <div className="flex flex-col items-center p-6 bg-white/50 rounded-xl backdrop-blur-sm border border-white/20">
              <div className="bg-accent-100 p-3 rounded-full mb-4">
                <Zap className="w-8 h-8 text-accent-600" />
              </div>
              <h3 className="text-lg font-semibold text-secondary-900 mb-2">ETL Focused</h3>
              <p className="text-secondary-600 text-center">
                Designed specifically for ETL pipeline development and management
              </p>
            </div>

            <div className="flex flex-col items-center p-6 bg-white/50 rounded-xl backdrop-blur-sm border border-white/20">
              <div className="bg-secondary-100 p-3 rounded-full mb-4">
                <Code className="w-8 h-8 text-secondary-600" />
              </div>
              <h3 className="text-lg font-semibold text-secondary-900 mb-2">Open Source</h3>
              <p className="text-secondary-600 text-center">
                Free and open source with a growing collection of utilities
              </p>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Background decoration */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-40 -right-32 w-80 h-80 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-slow"></div>
        <div className="absolute -bottom-40 -left-32 w-80 h-80 bg-accent-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse-slow"></div>
      </div>
    </section>
  )
}
