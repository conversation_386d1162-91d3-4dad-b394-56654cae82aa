'use client'

import { 
  Search, 
  Lightbulb, 
  Code, 
  GraduationCap, 
  HeadphonesIcon,
  ArrowRight,
  CheckCircle
} from 'lucide-react'
import { motion } from 'framer-motion'

export default function Process() {
  const processSteps = [
    {
      icon: Search,
      title: 'Discovery & Assessment',
      description: 'We start by understanding your current data infrastructure, challenges, and business objectives.',
      details: [
        'Current system analysis',
        'Performance bottleneck identification',
        'Requirements gathering',
        'Technical feasibility assessment'
      ],
      duration: '1-2 weeks',
      color: 'primary'
    },
    {
      icon: Lightbulb,
      title: 'Solution Design',
      description: 'Our experts design a tailored Jorvik-based solution that addresses your specific needs.',
      details: [
        'Architecture planning',
        'Technology stack selection',
        'Performance optimization strategy',
        'Implementation roadmap'
      ],
      duration: '1-3 weeks',
      color: 'accent'
    },
    {
      icon: Code,
      title: 'Implementation',
      description: 'We build and deploy your custom solution with rigorous testing and quality assurance.',
      details: [
        'Custom development',
        'Integration with existing systems',
        'Comprehensive testing',
        'Performance optimization'
      ],
      duration: '2-8 weeks',
      color: 'secondary'
    },
    {
      icon: GraduationCap,
      title: 'Knowledge Transfer',
      description: 'We ensure your team is fully equipped to maintain and extend the implemented solution.',
      details: [
        'Team training sessions',
        'Documentation handover',
        'Best practices workshop',
        'Q&A and troubleshooting'
      ],
      duration: '1-2 weeks',
      color: 'primary'
    },
    {
      icon: HeadphonesIcon,
      title: 'Ongoing Support',
      description: 'Continuous support and optimization to ensure your solution evolves with your needs.',
      details: [
        'Regular health checks',
        'Performance monitoring',
        'Feature enhancements',
        '24/7 support options'
      ],
      duration: 'Ongoing',
      color: 'accent'
    }
  ]

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'primary':
        return {
          bg: 'bg-primary-100',
          text: 'text-primary-600',
          border: 'border-primary-200',
          gradient: 'from-primary-50 to-primary-100'
        }
      case 'accent':
        return {
          bg: 'bg-accent-100',
          text: 'text-accent-600',
          border: 'border-accent-200',
          gradient: 'from-accent-50 to-accent-100'
        }
      case 'secondary':
        return {
          bg: 'bg-secondary-100',
          text: 'text-secondary-600',
          border: 'border-secondary-200',
          gradient: 'from-secondary-50 to-secondary-100'
        }
      default:
        return {
          bg: 'bg-primary-100',
          text: 'text-primary-600',
          border: 'border-primary-200',
          gradient: 'from-primary-50 to-primary-100'
        }
    }
  }

  return (
    <section id="process" className="py-20 px-4 sm:px-6 lg:px-8 bg-secondary-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
              Our Proven
              <span className="gradient-text"> Process</span>
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              We follow a structured approach to ensure successful project delivery, 
              from initial consultation to ongoing support and optimization.
            </p>
          </motion.div>
        </div>

        {/* Process Steps */}
        <div className="space-y-8">
          {processSteps.map((step, index) => {
            const colors = getColorClasses(step.color)
            const Icon = step.icon
            
            return (
              <motion.div
                key={step.title}
                initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`flex flex-col lg:flex-row items-center gap-8 ${
                  index % 2 === 1 ? 'lg:flex-row-reverse' : ''
                }`}
              >
                {/* Step Number & Icon */}
                <div className="flex-shrink-0">
                  <div className={`relative bg-gradient-to-br ${colors.gradient} rounded-2xl p-8 border ${colors.border}`}>
                    <div className="absolute -top-4 -left-4 bg-white rounded-full w-12 h-12 flex items-center justify-center border-4 border-primary-600 text-primary-600 font-bold text-lg">
                      {index + 1}
                    </div>
                    <Icon className={`w-12 h-12 ${colors.text}`} />
                  </div>
                </div>

                {/* Step Content */}
                <div className="flex-1 bg-white rounded-xl p-8 shadow-lg border border-secondary-200">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4">
                    <h3 className="text-2xl font-bold text-secondary-900 mb-2 lg:mb-0">
                      {step.title}
                    </h3>
                    <span className={`px-4 py-2 rounded-full text-sm font-medium ${colors.bg} ${colors.text}`}>
                      {step.duration}
                    </span>
                  </div>
                  
                  <p className="text-secondary-600 mb-6 leading-relaxed">
                    {step.description}
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {step.details.map((detail, detailIndex) => (
                      <div key={detailIndex} className="flex items-center">
                        <CheckCircle className={`w-4 h-4 mr-2 ${colors.text}`} />
                        <span className="text-secondary-700 text-sm">{detail}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-secondary-900 to-primary-900 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Ready to Get Started?</h3>
            <p className="text-secondary-100 mb-6 max-w-2xl mx-auto">
              Let's discuss your project requirements and how our proven process 
              can help you achieve your data engineering goals.
            </p>
            <a
              href="#contact"
              className="bg-white text-secondary-900 px-8 py-3 rounded-lg font-semibold hover:bg-secondary-50 transition-colors inline-flex items-center"
            >
              Start Your Project
              <ArrowRight className="ml-2 w-5 h-5" />
            </a>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
