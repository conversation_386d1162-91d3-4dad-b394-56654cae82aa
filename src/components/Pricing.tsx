'use client'

import { 
  Check, 
  Star, 
  ArrowRight, 
  Clock, 
  Users, 
  Zap,
  Shield,
  HeadphonesIcon
} from 'lucide-react'
import { motion } from 'framer-motion'

export default function Pricing() {
  const pricingModels = [
    {
      title: 'Project-Based Consulting',
      icon: Zap,
      description: 'Fixed-scope projects with defined deliverables and timelines',
      features: [
        'Comprehensive project scoping',
        'Fixed timeline and budget',
        'Dedicated project team',
        'Regular progress updates',
        'Quality assurance testing',
        'Documentation and handover'
      ],
      pricing: 'Starting at $25,000',
      duration: '4-12 weeks',
      bestFor: 'Specific implementations or migrations',
      popular: false,
      color: 'primary'
    },
    {
      title: 'Retainer Options',
      icon: Clock,
      description: 'Ongoing partnership with guaranteed availability and priority support',
      features: [
        'Guaranteed monthly hours',
        'Priority response times',
        'Strategic planning sessions',
        'Continuous optimization',
        'Team training included',
        'Flexible scope adjustments'
      ],
      pricing: '$15,000 - $50,000/month',
      duration: '3-12 month terms',
      bestFor: 'Long-term partnerships and continuous improvement',
      popular: true,
      color: 'accent'
    },
    {
      title: 'Training Packages',
      icon: Users,
      description: 'Comprehensive training programs to upskill your team',
      features: [
        'Customized curriculum',
        'Hands-on workshops',
        'Real-world case studies',
        'Certification program',
        'Follow-up support',
        'Training materials included'
      ],
      pricing: '$5,000 - $15,000',
      duration: '1-4 weeks',
      bestFor: 'Team development and knowledge transfer',
      popular: false,
      color: 'secondary'
    },
    {
      title: 'Support Tiers',
      icon: HeadphonesIcon,
      description: 'Ongoing support and maintenance for your Jorvik implementations',
      features: [
        'Bug fixes and patches',
        'Performance monitoring',
        'Regular health checks',
        'Version upgrades',
        'Emergency support',
        'Best practices guidance'
      ],
      pricing: '$2,500 - $10,000/month',
      duration: 'Ongoing',
      bestFor: 'Production systems requiring continuous support',
      popular: false,
      color: 'primary'
    }
  ]

  const openSourceCommitments = [
    {
      title: 'Community Contributions',
      description: 'Client work directly improves Jorvik for everyone',
      icon: '🌟'
    },
    {
      title: 'Open Source First',
      description: 'All general utilities developed are contributed back',
      icon: '🔓'
    },
    {
      title: 'Transparent Development',
      description: 'Public roadmap influenced by real-world client needs',
      icon: '👁️'
    },
    {
      title: 'Community Support',
      description: 'Active participation in forums and issue resolution',
      icon: '🤝'
    }
  ]

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'primary':
        return {
          bg: 'bg-primary-100',
          text: 'text-primary-600',
          border: 'border-primary-200',
          gradient: 'from-primary-50 to-primary-100'
        }
      case 'accent':
        return {
          bg: 'bg-accent-100',
          text: 'text-accent-600',
          border: 'border-accent-200',
          gradient: 'from-accent-50 to-accent-100'
        }
      case 'secondary':
        return {
          bg: 'bg-secondary-100',
          text: 'text-secondary-600',
          border: 'border-secondary-200',
          gradient: 'from-secondary-50 to-secondary-100'
        }
      default:
        return {
          bg: 'bg-primary-100',
          text: 'text-primary-600',
          border: 'border-primary-200',
          gradient: 'from-primary-50 to-primary-100'
        }
    }
  }

  return (
    <section id="pricing" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
              Flexible
              <span className="gradient-text"> Pricing Models</span>
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Choose the engagement model that best fits your project needs and budget. 
              All options include our commitment to open source excellence.
            </p>
          </motion.div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {pricingModels.map((model, index) => {
            const colors = getColorClasses(model.color)
            const Icon = model.icon
            
            return (
              <motion.div
                key={model.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`relative p-8 rounded-xl border-2 ${
                  model.popular ? 'border-accent-400 bg-gradient-to-br from-accent-50 to-white' : `${colors.border} bg-white`
                } hover:shadow-lg transition-shadow`}
              >
                {model.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-accent-600 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center">
                      <Star className="w-4 h-4 mr-1" />
                      Most Popular
                    </div>
                  </div>
                )}
                
                <div className="flex items-center mb-6">
                  <div className={`${colors.bg} p-3 rounded-lg mr-4`}>
                    <Icon className={`w-6 h-6 ${colors.text}`} />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-secondary-900">{model.title}</h3>
                    <p className="text-secondary-600">{model.description}</p>
                  </div>
                </div>
                
                <div className="mb-6">
                  <div className="text-3xl font-bold text-secondary-900 mb-1">{model.pricing}</div>
                  <div className="text-secondary-600 text-sm">{model.duration}</div>
                  <div className="text-secondary-500 text-sm mt-2">Best for: {model.bestFor}</div>
                </div>
                
                <ul className="space-y-3 mb-8">
                  {model.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center">
                      <Check className={`w-4 h-4 mr-3 ${colors.text}`} />
                      <span className="text-secondary-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <a
                  href="#contact"
                  className={`w-full ${
                    model.popular 
                      ? 'bg-accent-600 hover:bg-accent-700 text-white' 
                      : `${colors.bg} ${colors.text} hover:opacity-80`
                  } px-6 py-3 rounded-lg font-semibold transition-colors inline-flex items-center justify-center`}
                >
                  Get Started
                  <ArrowRight className="ml-2 w-4 h-4" />
                </a>
              </motion.div>
            )
          })}
        </div>

        {/* Open Source Commitment */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-secondary-900 to-primary-900 rounded-2xl p-8 text-white"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold mb-4">Our Open Source Commitment</h3>
            <p className="text-secondary-100 max-w-2xl mx-auto">
              When you work with us, you're not just getting expert consulting – you're contributing 
              to the growth and improvement of the Jorvik ecosystem for everyone.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {openSourceCommitments.map((commitment, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl mb-3">{commitment.icon}</div>
                <h4 className="font-semibold mb-2">{commitment.title}</h4>
                <p className="text-secondary-200 text-sm">{commitment.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-secondary-50 rounded-xl p-8">
            <h3 className="text-2xl font-bold text-secondary-900 mb-4">
              Ready to Discuss Your Project?
            </h3>
            <p className="text-secondary-600 mb-6 max-w-2xl mx-auto">
              Every project is unique. Let's schedule a consultation to discuss your specific 
              requirements and determine the best approach for your success.
            </p>
            <a
              href="#contact"
              className="bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors inline-flex items-center"
            >
              Schedule Free Consultation
              <ArrowRight className="ml-2 w-5 h-5" />
            </a>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
